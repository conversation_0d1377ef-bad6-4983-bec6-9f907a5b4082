version: '3'
services:
  rjgf-hb-inspect-web:
    image: 192.168.10.102:8110/k8s/rjgf-hb-inspect-web
    container_name: rjgf-hb-inspect-web
    restart: always
    ports:
      - "31281:8080"
    environment:
      API_PATH: http://192.168.10.102:31151
    labels:
      kompose.service.type: nodeport
    deploy:
      resources:
        limits:
          memory: 4G
        reservations:
          memory: 1G