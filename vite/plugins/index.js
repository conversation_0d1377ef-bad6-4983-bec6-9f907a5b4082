import vue from '@vitejs/plugin-vue'

import createAutoImport from './auto-import'
import createSvgIcon from './svg-icon'
import createCompression from './compression'
import createSetupExtend from './setup-extend'

export default function createVitePlugins(viteEnv, isBuild = false) {
    // 获取当前模式，判断是否为生产环境
    const isProduction = viteEnv.VITE_APP_ENV === 'production'

    // Vue 插件配置
    const vuePluginOptions = {
        // 在非生产环境下启用 Vue DevTools 支持
        reactivityTransform: true
    }

    const vitePlugins = [vue(vuePluginOptions)]
    vitePlugins.push(createAutoImport())
	vitePlugins.push(createSetupExtend())
    vitePlugins.push(createSvgIcon(isBuild))
	isBuild && vitePlugins.push(...createCompression(viteEnv))
    return vitePlugins
}
