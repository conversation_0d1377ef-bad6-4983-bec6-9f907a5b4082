<template>
  <a-modal
    title="设置各类告警切换周期"
    width="300px"
    :visible="visible"
    @ok="handleOk"
    @cancel="handleCancel"
    class="modal-box"
  >
    <div class="time-box">
      <span>切换周期 :</span>
      <a-input v-model="tiem" suffix="秒" style="width: 160px" />
    </div>
  </a-modal>
</template>

<script>
// import Vue from 'vue'
export default {
  name: 'modalForm',
  data() {
    return {
      title: '',
      visible: false,
      key: {},
      tiem: 10,
    }
  },
  created() {},
  computed: {},
  watch: {},
  methods: {
    initForm(data) {
      // this.tiem = Vue.ls.get('changeTime') ? Vue.ls.get('changeTime') : 10
      this.visible = true
    },
    close() {
      this.visible = false
    },
    handleOk() {
      this.$emit('change', this.tiem)
      // Vue.ls.set('changeTime', this.tiem)
      this.close()
    },
    handleCancel() {
      this.close()
    },
  },
}
</script>

<style scoped lang="scss">
.modal-box {
  :deep() .ant-modal-content {
    background-color: #065e89;
    opacity: 0.9;
    .ant-modal-header {
      background: #065e89;
      opacity: 0.9;
      .ant-modal-title {
        font-size: 16px;
        color: #fff;
      }
    }
    .ant-modal-body {
      padding: 0 10px;
    }
    .ant-modal-close {
      color: #fff;
    }
  }
}
.time-box {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
  span {
    padding-right: 10px;
    color: #fff;
    font-size: 14px;
  }
}
</style>
