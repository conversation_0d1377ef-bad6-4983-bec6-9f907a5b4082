<template>
  <pageLayout @on-submit="handleClick('submit')" @on-reset="handleClick('reset')" @on-refresh="handleClick('refresh')">
    <template #layout-query>
      <el-row>
        <el-col :span="6">
          <div class="page-grid-content">
            <span class="page-query-title">归档时间：</span>
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD HH:mm:ss"
              class="page-box-width"
              @change="handleDateChange"
              size="default"
            ></el-date-picker>
          </div>
        </el-col>
      </el-row>
    </template>
    <template #sticky-bar>
        <el-button class="page-red-btn" size="mini" v-if="level !== 'all-city'" @click="handleBack">返回上一级</el-button>
        <el-button type="info" size="small" @click="handleExport" :loading="loadingExport">导出</el-button>
    </template>
    <template #page-result>
      <!-- 所有地市： level all-city -->
      <el-table ref="mainTable" v-if="level === 'all-city'" style="height:calc(100% - 62px)" fit :data="tableList" v-loading="listLoading"
        class="page-table-style" header-cell-class-name="page-table-header" :row-class-name="tableRowClassName">
        <el-table-column label="序号" type="index" width="80px">
          <template #default="scope">
            <span>{{
              ((listQuery.pageNum - 1) * listQuery.pageSize + scope.$index + 1).toString().padStart(2, '0')
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="所属地市" min-width="120px" prop="dimension" show-overflow-tooltip>
          <template #default="scope">
            <span class="clickable-city" @click="handleCellClick(scope.row)">
              {{ scope.row.dimension }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="工单数量" min-width="120px" prop="total" show-overflow-tooltip />
        <el-table-column class-name="qualified" label="合格数量" min-width="120px" prop="quality" show-overflow-tooltip />
        <el-table-column class-name="unqualified" label="不合格数量" min-width="120px" prop="unQuality" show-overflow-tooltip />
        <el-table-column label="合格率" min-width="120px" prop="proportion" show-overflow-tooltip />
      </el-table>

      <!-- 单个地市： level one-city-county -->
      <el-table ref="mainTable" v-if="level === 'one-city-county'" style="height:calc(100% - 62px)" fit :data="tableList" v-loading="listLoading"
        class="page-table-style" header-cell-class-name="page-table-header" :row-class-name="tableRowClassName">
        <el-table-column label="序号" type="index" width="80px">
          <template #default="scope">
            <span>{{
              ((listQuery.pageNum - 1) * listQuery.pageSize + scope.$index + 1).toString().padStart(2, '0')
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="所属区县" min-width="120px" prop="dimension" show-overflow-tooltip >
          <template #default="scope">
            <span class="clickable-city" @click="handleCellClick(scope.row)">
              {{ scope.row.dimension }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="工单数量" min-width="120px" prop="total" show-overflow-tooltip />
        <el-table-column class-name="qualified" label="合格数量" min-width="120px" prop="quality" show-overflow-tooltip />
        <el-table-column class-name="unqualified" label="不合格数量" min-width="120px" prop="unQuality" show-overflow-tooltip />
        <el-table-column label="合格率" min-width="120px" prop="proportion" show-overflow-tooltip />
      </el-table>

      <!-- 清单级： level list -->
      <el-table ref="mainTable" v-if="level === 'list'" style="height:calc(100% - 62px)" fit :data="tableList" v-loading="listLoading"
        class="page-table-style" header-cell-class-name="page-table-header" :row-class-name="tableRowClassName">
        <el-table-column label="序号" type="index" width="80px">
          <template #default="scope">
            <span>{{
              ((listQuery.pageNum - 1) * listQuery.pageSize + scope.$index + 1).toString().padStart(2, '0')
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="是否合规" min-width="120px" show-overflow-tooltip>
          <template #default="scope">
            <span :class="scope.row.isQualityCompliance === '是' ? 'status-success' : 'status-danger'">
              {{ scope.row.isQualityCompliance }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="不合规明细" min-width="120px">
          <template #default="scope">
            <el-tooltip
              :content="scope.row.unComplianceDetail?.replace(/\n/g, '<br/>') ?? ''"
              raw-content
            >
              {{ scope.row.unComplianceDetail ? scope.row.unComplianceDetail.split('\n')[0] : '' }}
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="所属省份" min-width="120px" prop="province" show-overflow-tooltip />
        <el-table-column label="所属地市" min-width="120px" prop="city" show-overflow-tooltip />
        <el-table-column label="所属区县" min-width="120px" prop="county" show-overflow-tooltip />
        <el-table-column label="故障单编号" min-width="170px" prop="faultCode" show-overflow-tooltip />
        <el-table-column label="站址名称" min-width="260px" prop="siteName" show-overflow-tooltip />
        <el-table-column label="运维ID" min-width="170px" prop="operationId" show-overflow-tooltip />
        <el-table-column label="所属运营商" min-width="120px" prop="belongOperator" show-overflow-tooltip />
        <el-table-column label="是否发电工单" min-width="120px" show-overflow-tooltip>
          <template #default="scope">
            <span :class="scope.row.isElectricityWork === '是' ? 'status-success' : 'status-danger'">
              {{ scope.row.isElectricityWork }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="回单人" min-width="120px" prop="receiptPeople" show-overflow-tooltip />
        <el-table-column label="代维公司" min-width="170px" prop="dwCompany" show-overflow-tooltip />
        <el-table-column label="派单时间" min-width="170px" prop="dispatchTime" show-overflow-tooltip />
        <el-table-column label="接单时间" min-width="170px" prop="receiveTime" show-overflow-tooltip />
        <el-table-column label="回单时间" min-width="170px" prop="receiptTime" show-overflow-tooltip />
        <el-table-column label="归档时间" min-width="170px" prop="archivedTime" show-overflow-tooltip />
        <el-table-column label="操作区" min-width="100px" fixed="right" show-overflow-tooltip>
          <template #default="scope">
            <el-button class="page-red-btn" @click="handleDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    <pagination :total="total" :page.sync="listQuery.pageNum" :limit.sync="listQuery.pageSize"
      @pagination="handleCurrentChange" />
      <Detail ref="detail" @on-refresh="refreshBox" />
    </template>
  </pageLayout>
</template>
<script>
import pageLayout from "@/components/base/pageLayout";
import Detail from "./modal/elec_statistics_detail.vue";
import { getOrderList, getElecOrderPage, getListCity, getListCounty, exp, Listexp } from "@/api/elec/elec_order";

export default {
  name: 'elec_statistics_city',
  components: {
    pageLayout,
    Detail
  },
  data() {
    return {
      tableList: [],
      dateRange: null,
      level: 'all-city', // 'all-city' 'one-city-county' 'list'
      listQuery: {
        pageNum: 1,
        pageSize: 10,
        dimension: '地市',
        faultCode: '',
        siteName: '',
        operationId: '',
        belongOperatorList: [],
        cityList: '',
        countyList: '',
        isElectricityWork: '',
        isQualityCompliance: '',
        archivedStartTime: '',
        archivedEndTime: '',
      },
      listLoading: false,
      loadingExport: false,
      total: 0,
      baseURL: import.meta.env.VUE_APP_BASE_API,
      cityOptions: [],
      countyOptions: [],
    }
  },
  mounted() {
    this.getList();
    this.getCity();
  },
  methods: {
    async getList() {
      this.listLoading = true;

      const params = {
        ...this.listQuery,
      };

      try {
        if (this.level !== 'list') {
          const { result } = await getOrderList(params);
          this.total = result.length;
          this.tableList = result;
        } else {
          const { result } = await getElecOrderPage(params);
          this.total = result.total;
          this.tableList = result.list;
        }
      } catch (error) {
        console.error('获取电工单列表失败:', error);
        this.$message.error('获取电工单列表失败');
      } finally {
        this.listLoading = false;
      }
    },
    tableRowClassName({ rowIndex }) {
      return rowIndex % 2 === 0 ? "rowStyle" : "rowStyleWhite";
    },
    handleCellClick(row) {
      if (this.level === 'all-city') {
        this.level = 'one-city-county';
        this.listQuery.city = row.dimension;
        this.getList();
      } else if (this.level === 'one-city-county') {
        this.level = 'list';
        this.listQuery.cityList = this.listQuery.city;
        delete this.listQuery.city;
        this.listQuery.countyList = row.dimension;
        this.getList();
      }
    },
    handleBack() {
      if (this.level === 'one-city-county') {
        this.level = 'all-city';
        this.listQuery.city = '';
      } else if (this.level === 'list') {
        this.level = 'one-city-county';
        this.listQuery.city = this.listQuery.cityList;
        delete this.listQuery.cityList;
        delete this.listQuery.countyList;
      }
      this.getList();
    },
    async handleDetail(row) {
      this.$refs.detail.initForm(row);
    },
    handleCurrentChange(val) {
      this.listQuery.pageNum = val.page;
      this.listQuery.pageSize = val.limit;
      this.getList();
    },
    handleClick(type) {
      if (type === "submit") {
        this.handleFilter();
      } else if (type === "reset") {
        this.handleReset();
      } else if (type === "refresh") {
        this.handleRefresh();
      }
    },
    refreshBox() {
      this.handleFilter();
    },
    handleFilter() {
      this.listQuery.pageNum = 1;
      this.level = 'all-city';
      delete this.listQuery.city;
      delete this.listQuery.cityList;
      delete this.listQuery.countyList;
      this.getList();
    },
    handleReset() {
      this.dateRange = null;
      this.level = 'all-city';
      this.listQuery = {
        pageNum: 1,
        pageSize: 10,
        dimension: '地市',
        faultCode: '',
        siteName: '',
        operationId: '',
        belongOperatorList: [],
        cityList: '',
        countyList: '',
        isElectricityWork: '',
        isQualityCompliance: '',
        archivedStartTime: '',
        archivedEndTime: ''
      };
      this.countyOptions = [];
      this.getList();
    },
    handleRefresh() {
      this.getList();
    },
    handleDateChange(val) {
      if (val) {
        this.listQuery.archivedStartTime = val[0];
        this.listQuery.archivedEndTime = val[1];
      } else {
        this.listQuery.archivedStartTime = '';
        this.listQuery.archivedEndTime = '';
      }
    },
    async getCity() {
      try {
        const { result } = await getListCity();
        this.cityOptions = result || [];

        // 创建地市名称到代码的映射
        this.cityCodeMap = {};
        this.cityOptions.forEach(city => {
          this.cityCodeMap[city.regionName] = city.regionCode;
        });
      } catch (error) {
        console.error('获取地市列表失败:', error);
        this.$message.error('获取地市列表失败');
      }
    },
    async getCounty(cityList) {
      if (!cityList) return;

      try {
        const { result } = await getListCounty(cityList.map(x => x.regionCode));
        this.countyOptions = result || [];
      } catch (error) {
        console.error('获取区县列表失败:', error);
        this.$message.error('获取区县列表失败');
      }
    },
    async handleCityChange(values) {
      if (!values || values.length === 0) {
        this.listQuery.countyList = [];
        this.countyOptions = [];
        return;
      }
      await this.getCounty(values);
    },
    async handleExport() {
      try {
        this.loadingExport = true;
        const params = {
          ...this.listQuery,
        };
        const exportApi = this.level == 'list' ? Listexp : exp;

        const res = await exportApi(params);

        let filename = '发电工单统计-';

        if (this.level == 'all-city') {
          filename += `全省`;
        } else if (this.level == 'one-city-county') {
          filename += `${this.listQuery.city}`;
        } else {
          filename += `${this.listQuery.cityList}-${this.listQuery.countyList}`;
        }
        filename += `报表`;

        // 创建下载链接
        const blob = new Blob([res], { type: 'application/vnd.ms-excel' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.setAttribute('download', `${filename}.xlsx`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } catch (e) {
        this.$message.error('导出失败：' + (e.message || '请求未响应'));
      } finally {
        this.loadingExport = false;
      }
    },
  }
}
</script>
<style scoped>
:v-deep(.sticky) {
  margin: 10px 0 0;
  background-color: #fff;
  border-bottom: 1px solid #CD202D;
}

:deep(.qualified .cell.el-tooltip) {
  color: #007902;
}

:deep(.unqualified .cell.el-tooltip) {
  color: #CD202D;
}

.status-success {
  color: #67c23a;
  font-weight: bold;
}

.status-danger {
  color: #f56c6c;
  font-weight: bold;
}

.clickable-city {
  cursor: pointer;
  color: #000000;
  text-decoration: underline;
}

.compliance-text {
  cursor: pointer;
  display: inline-block;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
