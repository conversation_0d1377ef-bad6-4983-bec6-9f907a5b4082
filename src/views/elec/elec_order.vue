<template>
  <pageLayout @on-submit="handleClick('submit')" @on-reset="handleClick('reset')" @on-refresh="handleClick('refresh')">
    <template #layout-query>
      <el-row>
        <el-col :span="6">
          <div class="page-grid-content">
            <span class="page-query-title">故障单编号：</span>
            <el-input @keyup.enter.native="handleFilter" v-model="listQuery.faultCode" placeholder="请输入内容" clearable
              class="page-box-width" size="default">
            </el-input>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="page-grid-content">
            <span class="page-query-title">站址名称：</span>
            <el-input @keyup.enter.native="handleFilter" v-model="listQuery.siteName" placeholder="请输入内容" clearable
              class="page-box-width" size="default">
            </el-input>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="page-grid-content">
            <span class="page-query-title">站址运维ID：</span>
            <el-input @keyup.enter.native="handleFilter" v-model="listQuery.operationId" placeholder="请输入内容" clearable
              class="page-box-width" size="default">
            </el-input>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="page-grid-content">
            <span class="page-query-title">所属运营商：</span>
            <el-select v-model="listQuery.belongOperatorList" multiple collapse-tags placeholder="请选择" size="default" class="page-box-width" clearable>
              <el-option label="移动" value="移动"></el-option>
              <el-option label="联通" value="联通"></el-option>
              <el-option label="电信" value="电信"></el-option>
              <el-option label="广电" value="广电"></el-option>
            </el-select>
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <div class="page-grid-content">
            <span class="page-query-title">所属地市：</span>
            <el-select
              v-model="listQuery.cityList"
              value-key="regionCode"
              multiple
              collapse-tags
              filterable
              clearable
              placeholder="请选择地市"
              class="page-box-width"
              @change="handleCityChange"
            >
              <el-option
                v-for="item in cityOptions"
                :key="item.regionCode"
                value-key="regionCode"
                :label="item.regionName"
                :value="item"
              />
            </el-select>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="page-grid-content">
            <span class="page-query-title">所属区县：</span>
            <el-select
              v-model="listQuery.countyList"
              value-key="regionCode"
              multiple
              collapse-tags
              filterable
              clearable
              placeholder="请选择区县"
              class="page-box-width"
            >
              <el-option
                v-for="item in countyOptions"
                :key="item.regionCode"
                :label="item.regionName"
                :value="item"
              />
            </el-select>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="page-grid-content">
            <span class="page-query-title">是否发电工单：</span>
            <el-select v-model="listQuery.isElectricityWork" placeholder="请选择" size="default" class="page-box-width" clearable>
              <el-option label="是" value="是"></el-option>
              <el-option label="否" value="否"></el-option>
            </el-select>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="page-grid-content">
            <span class="page-query-title">是否合规：</span>
            <el-select v-model="listQuery.isQualityCompliance" placeholder="请选择" size="default" class="page-box-width" clearable>
              <el-option label="是" value="是"></el-option>
              <el-option label="否" value="否"></el-option>
            </el-select>
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <div class="page-grid-content">
            <span class="page-query-title">归档时间：</span>
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD HH:mm:ss"
              class="page-box-width"
              @change="handleDateChange"
              size="default"
            ></el-date-picker>
          </div>
        </el-col>
      </el-row>
    </template>
    <template #sticky-bar>
        <el-button type="info" size="mini" @click="handleExport" :loading="loadingExport">导出</el-button>
    </template>
    <template #page-result>
      <el-table ref="mainTable" style="height:calc(100% - 62px)" fit :data="tableList" v-loading="listLoading"
        class="page-table-style" header-cell-class-name="page-table-header" :row-class-name="tableRowClassName">
        <el-table-column label="序号" type="index" width="80px">
          <template #default="scope">
            <span>{{
              ((listQuery.pageNum - 1) * listQuery.pageSize + scope.$index + 1).toString().padStart(2, '0')
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="是否合规" min-width="120px" show-overflow-tooltip>
          <template #default="scope">
            <span :class="scope.row.isQualityCompliance === '是' ? 'status-success' : 'status-danger'">
              {{ scope.row.isQualityCompliance }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="不合规明细" min-width="120px">
          <template #default="scope">
            <el-tooltip
              :content="scope.row.unComplianceDetail?.replace(/\n/g, '<br/>') ?? ''"
              raw-content
            >
              {{ scope.row.unComplianceDetail ? scope.row.unComplianceDetail.split('\n')[0] : '' }}
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="所属省份" min-width="120px" prop="province" show-overflow-tooltip />
        <el-table-column label="所属地市" min-width="120px" prop="city" show-overflow-tooltip />
        <el-table-column label="所属区县" min-width="120px" prop="county" show-overflow-tooltip />
        <el-table-column label="故障单编号" min-width="170px" prop="faultCode" show-overflow-tooltip />
        <el-table-column label="站址名称" min-width="260px" prop="siteName" show-overflow-tooltip />
        <el-table-column label="运维ID" min-width="170px" prop="operationId" show-overflow-tooltip />
        <el-table-column label="所属运营商" min-width="120px" prop="belongOperator" show-overflow-tooltip />
        <el-table-column label="是否发电工单" min-width="120px" show-overflow-tooltip>
          <template #default="scope">
            <span :class="scope.row.isElectricityWork === '是' ? 'status-success' : 'status-danger'">
              {{ scope.row.isElectricityWork }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="回单人" min-width="120px" prop="receiptPeople" show-overflow-tooltip />
        <el-table-column label="代维公司" min-width="170px" prop="dwCompany" show-overflow-tooltip />
        <el-table-column label="派单时间" min-width="170px" prop="dispatchTime" show-overflow-tooltip />
        <el-table-column label="接单时间" min-width="170px" prop="receiveTime" show-overflow-tooltip />
        <el-table-column label="回单时间" min-width="170px" prop="receiptTime" show-overflow-tooltip />
        <el-table-column label="归档时间" min-width="170px" prop="archivedTime" show-overflow-tooltip />
        <el-table-column label="操作区" min-width="100px" fixed="right" show-overflow-tooltip>
          <template #default="scope">
            <el-button class="page-red-btn" @click="handleDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination :total="total" :page.sync="listQuery.pageNum" :limit.sync="listQuery.pageSize"
        @pagination="handleCurrentChange" />
      <Detail ref="detail" @on-refresh="refreshBox" />
    </template>
  </pageLayout>
</template>
<script>
import pageLayout from "@/components/base/pageLayout";
import Detail from "./modal/elec_order_detail.vue";
import { getElecOrderPage, getListCity, getListCounty, Listexp } from "@/api/elec/elec_order";

export default {
  name: 'ElecOrderList',
  components: {
    pageLayout,
    Detail
  },
  data() {
    return {
      tableList: [],
      dateRange: null,
      listQuery: {
        pageNum: 1,
        pageSize: 10,
        faultCode: '',
        siteName: '',
        operationId: '',
        belongOperatorList: [],
        cityList: [],
        countyList: [],
        isElectricityWork: '',
        isQualityCompliance: '',
        archivedStartTime: '',
        archivedEndTime: ''
      },
      listLoading: false,
      total: 0,
      baseURL: import.meta.env.VUE_APP_BASE_API,
      cityOptions: [],
      countyOptions: [],
    }
  },
  mounted() {
    this.getList();
    this.getCity();
  },
  methods: {
    async getList() {
      this.listLoading = true;

      // 处理查询参数
      const params = { ...this.listQuery };

      // 处理日期范围
      if (this.dateRange && this.dateRange.length === 2) {
        params.startTime = this.dateRange[0];
        params.endTime = this.dateRange[1];
      }

      // 处理地市和区县参数，将regionName转换为regionCode
      if (params.cityList && params.cityList.length > 0) {
        params.cityList = params.cityList.map(x => x.regionName);
      }
      if (params.countyList && params.countyList.length > 0) {
        params.countyList = params.countyList.map(x => x.regionName);
      }

      try {
        const { result } = await getElecOrderPage(params);
        this.total = result.total;
        this.tableList = result.list;
      } catch (error) {
        console.error('获取电工单列表失败:', error);
        this.$message.error('获取电工单列表失败');
      } finally {
        this.listLoading = false;
      }
    },
    tableRowClassName({ rowIndex }) {
      return rowIndex % 2 === 0 ? "rowStyle" : "rowStyleWhite";
    },
    async handleDetail(row) {
      this.$refs.detail.initForm(row);
    },
    handleCurrentChange(val) {
      this.listQuery.pageNum = val.page;
      this.listQuery.pageSize = val.limit;
      this.getList();
    },
    handleClick(type) {
      if (type === "submit") {
        this.handleFilter();
      } else if (type === "reset") {
        this.handleReset();
      } else if (type === "refresh") {
        this.handleRefresh();
      }
    },
    refreshBox() {
      this.handleFilter();
    },
    handleFilter() {
      this.listQuery.pageNum = 1;
      this.getList();
    },
    handleReset() {
      this.dateRange = null;
      this.listQuery = {
        pageNum: 1,
        pageSize: 10,
        faultCode: '',
        siteName: '',
        operationId: '',
        belongOperatorList: [],
        cityList: [],
        countyList: [],
        isElectricityWork: '',
        isQualityCompliance: '',
        archivedStartTime: '',
        archivedEndTime: ''
      };
      this.countyOptions = [];
      this.getList();
    },
    handleRefresh() {
      this.getList();
    },
    handleDateChange(val) {
      if (val) {
        this.listQuery.archivedStartTime = val[0];
        this.listQuery.archivedEndTime = val[1];
      } else {
        this.listQuery.archivedStartTime = '';
        this.listQuery.archivedEndTime = '';
      }
    },
    async getCity() {
      try {
        const { result } = await getListCity();
        this.cityOptions = result || [];

        // 创建地市名称到代码的映射
        this.cityCodeMap = {};
        this.cityOptions.forEach(city => {
          this.cityCodeMap[city.regionName] = city.regionCode;
        });
      } catch (error) {
        console.error('获取地市列表失败:', error);
        this.$message.error('获取地市列表失败');
      }
    },
    async getCounty(cityList) {
      if (!cityList) return;

      try {
        const { result } = await getListCounty(cityList.map(x => x.regionCode));
        this.countyOptions = result || [];
      } catch (error) {
        console.error('获取区县列表失败:', error);
        this.$message.error('获取区县列表失败');
      }
    },
    async handleCityChange(values) {
      if (!values || values.length === 0) {
        this.listQuery.countyList = [];
        this.countyOptions = [];
        return;
      }
      await this.getCounty(values);
    },
    async handleExport() {
        try {
            this.loadingExport = true;
            const params = { ...this.listQuery };
            const res = await Listexp(params);
            const blob = new Blob([res], { type: 'application/vnd.ms-excel' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.setAttribute('download', '发电报表稽核列表' + '.xlsx')
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
        } catch (e) {
            this.$message.error('导出失败：' + (e.message || '未知错误'));
        } finally {
            this.loadingExport = false;
        }
    },
  }
}
</script>
<style scoped>
:v-deep(.sticky) {
  margin: 10px 0 0;
  background-color: #fff;
  border-bottom: 1px solid #CD202D;
}

.status-success {
  color: #67c23a;
  font-weight: bold;
}

.status-danger {
  color: #f56c6c;
  font-weight: bold;
}
</style>
