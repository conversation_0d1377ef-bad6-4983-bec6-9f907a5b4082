<template>
    <el-dialog :title="this.isEdit ? '工单编辑' : '工单详情'" v-model="dialogVisible" :close-on-click-modal="false"
      destroy-on-close @close="handleClose" width="80%" class="common-dialog common-dialog-form common-dialog-body-10">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="工单信息" name="orderInfo">
          <el-form label-width="120px" ref="form" :model="form">
            <el-row>
              <el-col :span="8">
                <el-form-item label="故障单编号：">
                  <div>{{ form?.orderMsg?.faultCode }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="派单时间：">
                  <div>{{ form?.orderMsg?.dispatchTime }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="首次反馈时间：">
                  <div>{{ form?.orderMsg?.feedbackTime }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="回单人：">
                  <div>{{ form?.orderMsg?.receiptPeople }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="代维公司：">
                  <div>{{ form?.orderMsg?.dwCompany }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="是否发电工单：">
                  <div>{{ form?.orderMsg?.isElectricityWork }}</div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
  
        <el-tab-pane label="告警信息" name="alarmInfo">
          <el-table :data="form?.alarmMsg" style="width: 100%" border>
            <el-table-column prop="alarmName" label="告警名称" min-width="150"></el-table-column>
            <el-table-column prop="deviceName" label="设备名称" min-width="150"></el-table-column>
            <el-table-column prop="alarmGrade" label="告警等级" min-width="100"></el-table-column>
            <el-table-column prop="alarmElectricityTime" label="告警发电时间" min-width="150"></el-table-column>
            <el-table-column prop="alarmDetails" label="告警详情" min-width="200"></el-table-column>
            <el-table-column prop="clearTime" label="告警清除时间" min-width="150"></el-table-column>
          </el-table>
        </el-tab-pane>
  
        <el-tab-pane label="站址信息" name="siteInfo">
          <el-form label-width="120px" ref="siteForm" :model="form">
            <el-row>
              <el-col :span="8">
                <el-form-item label="站址名称：">
                  <div>{{ form?.siteMsg?.siteName }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="运维ID：">
                  <div>{{ form?.siteMsg?.operationId }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="所属省份：">
                  <div>{{ form?.siteMsg?.province }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="所属地市：">
                  <div>{{ form?.siteMsg?.city }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="所属区县：">
                  <div>{{ form?.siteMsg?.county }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="故障设备类型：">
                  <div>{{ form?.siteMsg?.faultEquipmentType }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="发电条件：">
                  <div>{{ form?.siteMsg?.isElectricity }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="所属运营商：">
                  <div>{{ form?.siteMsg?.belongOperator }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="FSU状态：">
                  <div>{{ form?.siteMsg?.fsuState }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="站址类型：">
                  <div>{{ form?.siteMsg?.siteType }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="站址经度：">
                  <div>{{ form?.siteMsg?.siteLong }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="站址纬度：">
                  <div>{{ form?.siteMsg?.siteLat }}</div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
  
        <el-tab-pane label="操作记录" name="operateLog">
          <el-table :data="form?.operateLogMsg" style="width: 100%" border>
            <el-table-column prop="faultCode" label="故障单编号" min-width="150"></el-table-column>
            <el-table-column prop="operator" label="操作人" min-width="120"></el-table-column>
            <el-table-column prop="operatoMotion" label="操作动作" min-width="120"></el-table-column>
            <el-table-column prop="operatoDescribe" label="操作描述" min-width="200"></el-table-column>
            <el-table-column prop="operatoTime" label="操作时间" min-width="150"></el-table-column>
            <el-table-column prop="copeTime" label="处理时长(分)" min-width="120"></el-table-column>
          </el-table>
        </el-tab-pane>
  
        <el-tab-pane label="发电信息" name="powerInfo">
          <el-form label-width="130px" ref="powerForm" :model="form">
            <el-row>
              <el-col :span="8">
                <el-form-item label="发电开始时间：">
                  <div>{{ form?.powerMsg?.electricityStartTime }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="发电结束时间：">
                  <div>{{ form?.powerMsg?.electricityEndTime }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
              <el-form-item label="外市电恢复时间：">
                <div>{{ form?.powerMsg?.electricRecoveryTime }}</div>
              </el-form-item>
            </el-col>
            </el-row>
            <el-row v-if="curvedSurveyImg">
              <el-col :span="24">
                <el-form-item label="曲测图：">
                  <div class="curved-survey-container">
                    <!-- 预览图片 -->
                    <el-image :src="curvedSurveyImg" class="curved-survey-img" :preview-src-list="[curvedSurveyImg]" fit="contain" style="max-width: 100%; max-height: 400px;"></el-image>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row v-else-if="loadingCurvedSurvey">
              <el-col :span="24">
                <el-form-item label="曲测图：">
                  <div class="curved-survey-loading">
                    <el-icon class="is-loading"><Loading /></el-icon>
                    <span>加载中...</span>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row v-else-if="form?.orderMsg?.faultCode">
              <el-col :span="24">
                <el-form-item label="曲测图：">
                  <div class="curved-survey-empty">
                    <el-button type="primary" @click="loadCurvedSurvey" size="small">加载曲线图</el-button>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
  
        <el-tab-pane label="回单信息" name="receiptInfo">
          <el-form label-width="120px" ref="receiptForm" :model="form">
            <el-row>
              <el-col :span="8">
                <el-form-item label="回单时间：">
                  <div>{{ form?.receiptMsg?.receiptTime }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="故障类型：">
                  <div>{{ form?.receiptMsg?.faultType }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="故障原因：">
                  <div>{{ form?.receiptMsg?.faultReason }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="处理措施：">
                  <div>{{ form?.receiptMsg?.handlingMeasures }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="归档时间：">
                  <div>{{ form?.receiptMsg?.archivedTime }}</div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
  
        <el-tab-pane label="附件列表" name="attachInfo">
          <el-table :data="form?.attachMsg" style="width: 100%" border>
            <el-table-column prop="attachName" label="附件名" min-width="200"></el-table-column>
            <el-table-column prop="uploadTime" label="上传时间" min-width="150"></el-table-column>
            <el-table-column prop="uploadBy" label="上传者" min-width="120"></el-table-column>
            <el-table-column label="操作" min-width="100">
              <template #default="scope">
                <el-button type="primary" size="small" @click="downloadAttach(scope.row)">下载</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
  
        <el-tab-pane label="质检规范" name="qaRule">
          <el-form label-width="180px" ref="qaForm" :model="form">
            <el-row>
              <el-col :span="8">
                <el-form-item label="反馈规范：">
                  <div :class="form?.qaRuleMsg?.feedbackSpecification === '是' ? 'status-success' : 'status-danger'"
                    v-html="form?.qaRuleMsg?.feedbackSpecification?.replace(/\n/g, '<br>') ?? ''"
                    raw-content
                  > 
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="工单处理动作规范：">
                  <div :class="form?.qaRuleMsg?.orderHandleActionSpecification === '是' ? 'status-success' : 'status-danger'"
                    v-html="form?.qaRuleMsg?.orderHandleActionSpecification?.replace(/\n/g, '<br>') ?? ''"
                    raw-content
                  > 
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="照片规范性：">
                  <div :class="form?.qaRuleMsg?.photoSpecification === '是' ? 'status-success' : 'status-danger'"
                    v-html="form?.qaRuleMsg?.photoSpecification?.replace(/\n/g, '<br>') ?? ''"
                    raw-content
                  > 
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="回单及时性：">
                  <div :class="form?.qaRuleMsg?.receiptTimeLy === '是' ? 'status-success' : 'status-danger'"
                    v-html="form?.qaRuleMsg?.receiptTimeLy?.replace(/\n/g, '<br>') ?? ''"
                    raw-content
                  > 
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="发电开始时间真实性：">
                  <div :class="form?.qaRuleMsg?.elecStartTimeTrueLy === '是' ? 'status-success' : 'status-danger'"
                    v-html="form?.qaRuleMsg?.elecStartTimeTrueLy?.replace(/\n/g, '<br>') ?? ''"
                    raw-content
                  > 
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="发电结束时间真实性：">
                  <div :class="form?.qaRuleMsg?.elecEndTimeTrueLy === '是' ? 'status-success' : 'status-danger'"
                    v-html="form?.qaRuleMsg?.elecEndTimeTrueLy?.replace(/\n/g, '<br>') ?? ''"
                    raw-content
                  > 
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="外市电恢复规范性：">
                  <div :class="form?.qaRuleMsg?.outCityReTimeSpecification === '是' ? 'status-success' : 'status-danger'"
                    v-html="form?.qaRuleMsg?.outCityReTimeSpecification?.replace(/\n/g, '<br>') ?? ''"
                    raw-content
                  > 
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="发电有效性：">
                  <div :class="form?.qaRuleMsg?.elecValidity === '是' ? 'status-success' : 'status-danger'"
                    v-html="form?.qaRuleMsg?.elecValidity?.replace(/\n/g, '<br>') ?? ''"
                    raw-content
                  > 
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="稽核时间：">
                  <div>{{ form?.qaRuleMsg?.qcTime }}</div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
      </el-tabs>
  
      <template #footer class="dialog-footer">
        <el-button @click="dialogVisible = false">关 闭</el-button>
      </template>
    </el-dialog>
  </template>
  <script>
  import { getElecOrderDetail, getCurvedSurvey, downloadAttachment } from "@/api/elec/elec_order";
  import { Loading } from '@element-plus/icons-vue';
  
  export default {
    name: 'ElecOrderDetail',
    components: {
      Loading
    },
    data() {
      return {
        title: '详情',
        dialogVisible: false,
        activeTab: 'orderInfo',
        curvedSurveyImg: '',
        loadingCurvedSurvey: false,
        form: {
          id: '',
          orderMsg: {
            dispatchTime: '',
            dwCompany: '',
            faultCode: '',
            feedbackTime: '',
            isElectricityWork: '',
            receiptPeople: ''
          },
          alarmMsg: [],
          siteMsg: {
            belongOperator: '',
            city: '',
            county: '',
            faultEquipmentType: '',
            fsuState: '',
            isElectricity: '',
            operationId: '',
            province: '',
            siteLat: 0,
            siteLong: 0,
            siteName: '',
            siteType: ''
          },
          operateLogMsg: [],
          powerMsg: {
            curvedSurveyPath: '',
            electricityEndTime: '',
            electricityStartTime: ''
          },
          receiptMsg: {
            archivedTime: '',
            faultReason: '',
            faultType: '',
            handlingMeasures: '',
            receiptTime: ''
          },
          attachMsg: [],
          qaRuleMsg: {
            elecEndTimeTrueLy: '',
            elecStartTimeTrueLy: '',
            elecValidity: '',
            feedbackSpecification: '',
            orderHandleActionSpecification: '',
            outCityReTimeSpecification: '',
            photoSpecification: '',
            qcTime: '',
            receiptTimeLy: ''
          }
        },
        isEdit: false
      };
    },
    methods: {
      initForm(data) {
        this.dialogVisible = true;
        if (data) {
          this.form = {
            ...this.form,
            id: data.id
          };
        }
        this.getDetail();
      },
      async getDetail() {
        try {
          const { result } = await getElecOrderDetail(this.form);
          this.form = {
            ...this.form,
            orderMsg: result.orderMsg || {},
            alarmMsg: result.alarmMsg || [],
            siteMsg: result.siteMsg || {},
            operateLogMsg: result.operateLogMsg || [],
            powerMsg: result.powerMsg || {},
            receiptMsg: result.receiptMsg || {},
            attachMsg: result.attachMsg || [],
            qaRuleMsg: result.qaRuleMsg || {}
          };
        } catch (error) {
          console.error('获取详情失败:', error);
          this.$message.error('获取详情失败');
        }
      },
      handleTabClick(tab) {
        if (tab.props.name === 'powerInfo' && this.form?.orderMsg?.faultCode && !this.curvedSurveyImg) {
          this.loadCurvedSurvey();
        }
      },
      async loadCurvedSurvey() {
        if (!this.form?.orderMsg?.faultCode) return;
  
        try {
          this.loadingCurvedSurvey = true;
          const { result } = await getCurvedSurvey(this.form.orderMsg.faultCode);
          if (result && result.content) {
            this.curvedSurveyImg = `data:image/png;base64,${result.content}`;
          } else {
            this.$message.warning('未找到曲线图数据');
          }
        } catch (error) {
          console.error('获取曲线图失败:', error);
          this.$message.error('获取曲线图失败');
        } finally {
          this.loadingCurvedSurvey = false;
        }
      },
      async downloadAttach(row) {
        if (!row?.id) {
          this.$message.warning('附件ID不存在');
          return;
        }
  
        try {
          const blob = await downloadAttachment(row.id);
          const link = document.createElement('a');
          link.href = window.URL.createObjectURL(blob);
          link.setAttribute('download', row.attachName || 'download');
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(link.href);
        } catch (error) {
          console.error('下载附件失败:', error);
          this.$message.error('下载附件失败');
        }
      },
      handleClose() {
        this.close();
      },
      close() {
        this.isEdit = false;
        this.dialogVisible = false;
        this.activeTab = 'orderInfo';
        this.curvedSurveyImg = '';
        this.loadingCurvedSurvey = false;
        this.form = {
          id: '',
          orderMsg: {
            dispatchTime: '',
            dwCompany: '',
            faultCode: '',
            feedbackTime: '',
            isElectricityWork: '',
            receiptPeople: ''
          },
          alarmMsg: [],
          siteMsg: {
            belongOperator: '',
            city: '',
            county: '',
            faultEquipmentType: '',
            fsuState: '',
            isElectricity: '',
            operationId: '',
            province: '',
            siteLat: 0,
            siteLong: 0,
            siteName: '',
            siteType: ''
          },
          operateLogMsg: [],
          powerMsg: {
            curvedSurveyPath: '',
            electricityEndTime: '',
            electricityStartTime: ''
          },
          receiptMsg: {
            archivedTime: '',
            faultReason: '',
            faultType: '',
            handlingMeasures: '',
            receiptTime: ''
          },
          attachMsg: [],
          qaRuleMsg: {
            elecEndTimeTrueLy: '',
            elecStartTimeTrueLy: '',
            elecValidity: '',
            feedbackSpecification: '',
            orderHandleActionSpecification: '',
            outCityReTimeSpecification: '',
            photoSpecification: '',
            qcTime: '',
            receiptTimeLy: ''
          }
        };
      }
    }
  }
  </script>
  <style lang="scss" scoped>
  .input-width {
    width: 95%;
  }
  
  .el-tabs {
    margin-bottom: 20px;
  }
  
  :deep(.el-table) {
    margin-bottom: 20px;
  }
  
  :deep(.el-form-item__content) {
    word-break: break-all;
  }
  
  .status-success {
    color: #67c23a;
    font-weight: bold;
  }
  
  .status-danger {
    color: #f56c6c;
    font-weight: bold;
  }
  
  .curved-survey-container {
    display: flex;
    justify-content: center;
    width: 100%;
    margin-top: 10px;
  }
  
  .curved-survey-img {
    max-width: 100%;
    max-height: 400px;
    object-fit: contain;
  }
  
  .curved-survey-loading,
  .curved-survey-empty {
    display: flex;
    align-items: center;
    margin-top: 10px;
  
    .el-icon {
      margin-right: 8px;
    }
  }
  
  .common-dialog-body-10 {
    :deep(.el-dialog__body) {
      padding: 10px 20px 0;
    }
  
    :deep(.el-loading-spinner) {
      top: 20% !important;
    }
  
    :deep(.el-dialog__footer) {
      margin-top: 2px;
    }
  }
  </style>