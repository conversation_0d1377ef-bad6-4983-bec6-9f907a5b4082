<template>
  <div class="flex-column app-container">
    <div class="flex-item">
      <div class="check-box">
        <div class="check-box-tab">
        <el-tabs
          v-model="activeName"
          @tab-click="handleClick"
        >
          <el-tab-pane  label="地市维度" name="city"></el-tab-pane>
          <el-tab-pane  label="代维公司维度" name="company"></el-tab-pane>
        </el-tabs>
        </div>
        <div class="component-box">
          <component :is="currentView"></component>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import pageLayout from "@/components/base/pageLayout";
import city from "./city";
import company from "./company";

export default {
  name: "elecStatistics",
  components: {
    pageLayout,
    city,
    company
  },
  data() {
    return {
      activeName: "city",
      currentView: "city",
    };
  },
  methods: {
    handleClick(tab) {
      this.currentView = tab.props.name;
    }
  },
  mounted() {
    // 组件挂载时可以添加初始化逻辑
  }
};
</script>
<style lang="scss" scoped>
.check-box {
  height: calc(100% - 22px);
  background: #fff;

  .component-box {
    height: calc(100% - 20px);
  }

  :deep(.page-query-bottom) {
    margin: 0 0 10px 0;
  }

  .tabs-style {
    background-color: #fff;
    margin-bottom: 9px;
  }

  // :deep(.el-tabs .el-tabs__nav-scroll) {
  //   height: 42px;
  // }
}

.check-box-tab{
  :deep(.el-tabs__nav-wrap::after) {
    background-color: transparent;
  }

  :deep(.el-tabs__item) {
    border: none;
  }

  :deep(.el-tabs__header) {
    margin: 0;
  }
}

:deep(.page-layout.app-container) {
  padding-left: 0;
  padding-right: 0;
  padding-bottom: 0;
}
</style>