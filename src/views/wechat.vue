<template>
  <pageLayout @on-submit="handleClick('submit')" @on-reset="handleClick('reset')" @on-refresh="handleClick('refresh')">
    <template #layout-query>
      <el-row>
        <el-col :span="6">
          <div class="page-grid-content">
            <span class="page-query-title">区县：</span>
            <el-select v-model="listQuery.county" placeholder="请选择" size="default" class="page-box-width" clearable>
              <el-option label="增城区" :value="'增城区'"></el-option>
              <el-option label="黄埔区" :value="'黄埔区'"></el-option>
              <el-option label="从化区" :value="'从化区'"></el-option>
              <el-option label="天河区" :value="'天河区'"></el-option>
              <el-option label="海珠区" :value="'海珠区'"></el-option>
              <el-option label="荔湾区" :value="'荔湾区'"></el-option>
              <el-option label="花都区" :value="'花都区'"></el-option>
              <el-option label="白云区" :value="'白云区'"></el-option>
              <el-option label="越秀区" :value="'越秀区'"></el-option>
              <el-option label="番禺区" :value="'番禺区'"></el-option>
              <el-option label="南沙区" :value="'南沙区'"></el-option>
            </el-select>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="page-grid-content">
            <span class="page-query-title">站址名称：</span>
            <el-input @keyup.enter.native="handleFilter" v-model="listQuery.siteName" placeholder="请输入内容" clearable
              class="page-box-width" size="default">
            </el-input>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="page-grid-content">
            <span class="page-query-title">站址编码：</span>
            <el-input @keyup.enter.native="handleFilter" v-model="listQuery.siteCode" placeholder="请输入内容" clearable
              class="page-box-width" size="default">
            </el-input>
          </div>
        </el-col>
      </el-row>
    </template>
    <template #sticky-bar>
      <el-button type="info" @click="handleDownLoad" :loading="loadingTemplate">下载模板</el-button>
      <span class="page-batch">
        <el-upload class="page-input-btn" :auto-upload="true" :on-success="uploadSuccess" :headers="tokenHeader"
          :before-upload="() => btnLoading = true" :show-file-list="false" name="formFile"
          :action="baseURL + `/ChttOperatorSiteTbls/Input`" :file-list="fileList">
          <el-button type="success" :loading="btnLoading" size="small">批量导入</el-button>
        </el-upload>
      </span>
      <el-button type="info" @click="handleExport" :loading="loadingExport">导出</el-button>
    </template>
    <template #page-result>
      <el-table ref="mainTable" style="height:calc(100% - 62px)" fit :data="tableList" v-loading="listLoading"
        class="page-table-style" header-cell-class-name="page-table-header" :row-class-name="tableRowClassName">
        <el-table-column label="序号" type="index" width="80px">
          <template #default="scope">
            <span>{{
              ((listQuery.page - 1) * listQuery.limit + scope.$index + 1).toString().padStart(2, '0')
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="地市" min-width="100px" prop="city" show-overflow-tooltip />
        <el-table-column label="区县" min-width="100px" prop="county" show-overflow-tooltip />
        <el-table-column label="网格名称" min-width="170px" prop="gridName" show-overflow-tooltip />
        <el-table-column label="站址名称" min-width="260px" prop="siteName" show-overflow-tooltip />
        <el-table-column label="站址编码" min-width="170px" prop="siteCode" show-overflow-tooltip />
        <el-table-column label="运维ID" min-width="170px" prop="operationId" show-overflow-tooltip />
        <el-table-column label="移动站址名称" min-width="170px" prop="moveSite" show-overflow-tooltip />
        <el-table-column label="联通站址名称" min-width="170px" prop="unicomSite" show-overflow-tooltip />
        <el-table-column label="电信站址名称" min-width="170px" prop="telecomSite" show-overflow-tooltip />
        <el-table-column label="操作区" min-width="100px" fixed="right" show-overflow-tooltip>
          <template #default="scope">
            <el-button class="page-red-btn" @click="handleDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit"
        @pagination="handleCurrentChange" />
      <wechat-detail ref="wechatDetail" @on-refresh="refreshBox" />
    </template>
  </pageLayout>
</template>
<script>
import pageLayout from "@/components/base/pageLayout";
// import { getToken } from "@/utils/auth";
import WechatDetail from "./modal/wechat-detail.vue";

const OperatorListMstsLoad = () => (
  {
    "code": 200,
    "msg": "加载成功",
    "count": 23356,
    "data": [
      {
        "city": "广州分公司",
        "region": "南区",
        "county": "海珠区",
        "gridName": "康乐网格",
        "siteName": "逸景第一小学西",
        "siteCode": "440105908000000055",
        "operationId": "44010500000352",
        "belongs": "",
        "moveSite": null,
        "unicomSite": "逸景第一小学西",
        "telecomSite": "逸景第一小学西",
        "creator": "",
        "creatorId": "",
        "createTime": "2024-08-13 16:20:31",
        "updator": "超级管理员",
        "updatorId": "00000000-0000-0000-0000-000000000000",
        "updateTime": "2024-08-21 09:08:32",
        "isDelete": false,
        "id": "0009107c-74cb-475e-93c9-10e72a924dce"
      },
      {
        "city": "广州分公司",
        "region": "北区",
        "county": "从化区",
        "gridName": "街口灌村温泉网格",
        "siteName": "温泉康复C网基站",
        "siteCode": "440184908000000066",
        "operationId": "44018400000053",
        "belongs": "",
        "moveSite": null,
        "unicomSite": "广州温泉康复N9改造",
        "telecomSite": "温泉康复C网基站",
        "creator": "",
        "creatorId": "",
        "createTime": "2024-08-13 16:20:31",
        "updator": "",
        "updatorId": "",
        "updateTime": "2024-08-13 16:20:31",
        "isDelete": false,
        "id": "000ee1a3-8a3f-40c9-aa25-40b853fbc8f0"
      },
      {
        "city": "广州分公司",
        "region": null,
        "county": "天河区",
        "gridName": "",
        "siteName": "广州华农跃进北宿舍",
        "siteCode": "440106010000002089",
        "operationId": "44010600003275",
        "belongs": "",
        "moveSite": null,
        "unicomSite": null,
        "telecomSite": null,
        "creator": "",
        "creatorId": "",
        "createTime": "2024-08-13 16:20:31",
        "updator": "",
        "updatorId": "",
        "updateTime": "2024-08-13 16:20:31",
        "isDelete": false,
        "id": "00184e03-d1b7-42c3-b507-c2c052344b06"
      },
      {
        "city": "广州分公司",
        "region": "南区",
        "county": "海珠区",
        "gridName": "昌岗网格",
        "siteName": "前进路南院大街C网基站",
        "siteCode": "440105908000000745",
        "operationId": "44010500000078",
        "belongs": "",
        "moveSite": null,
        "unicomSite": null,
        "telecomSite": "前进路南院大街C网基站",
        "creator": "",
        "creatorId": "",
        "createTime": "2024-08-13 16:20:31",
        "updator": "",
        "updatorId": "",
        "updateTime": "2024-08-13 16:20:31",
        "isDelete": false,
        "id": "00208d25-c639-490c-8e48-fffa49c450ba"
      },
      {
        "city": "广州分公司",
        "region": "北区",
        "county": "从化区",
        "gridName": "江埔太平网格",
        "siteName": "软件学院西-2",
        "siteCode": "440184908000000857",
        "operationId": "44018400001079",
        "belongs": "",
        "moveSite": null,
        "unicomSite": "广州软件学院西改造1",
        "telecomSite": null,
        "creator": "",
        "creatorId": "",
        "createTime": "2024-08-13 16:20:31",
        "updator": "",
        "updatorId": "",
        "updateTime": "2024-08-13 16:20:31",
        "isDelete": false,
        "id": "00273fc5-3e5a-43b0-a30d-52b929d6a83f"
      },
      {
        "city": "广州分公司",
        "region": "南区",
        "county": "番禺区",
        "gridName": "",
        "siteName": "佛莞城际铁路-隧道",
        "siteCode": "440113010010004022",
        "operationId": "44011300006857",
        "belongs": "",
        "moveSite": "广州FG番禺区广州-FG-001-番禺区佛莞长隆隧道西左一DF-HLR",
        "unicomSite": null,
        "telecomSite": null,
        "creator": "",
        "creatorId": "",
        "createTime": "2024-08-13 16:20:31",
        "updator": "",
        "updatorId": "",
        "updateTime": "2024-08-13 16:20:31",
        "isDelete": false,
        "id": "002c74d6-50ee-48fa-b100-e7f43a9d12e0"
      },
      {
        "city": "广州分公司",
        "region": "南区",
        "county": "番禺区",
        "gridName": "钟村网格",
        "siteName": "广州pan禺屏山(搬迁)",
        "siteCode": "440113908000001181",
        "operationId": "44011300001249",
        "belongs": "",
        "moveSite": "广州番禺屏山(搬迁)",
        "unicomSite": null,
        "telecomSite": null,
        "creator": "",
        "creatorId": "",
        "createTime": "2024-08-13 16:20:31",
        "updator": "",
        "updatorId": "",
        "updateTime": "2024-08-13 16:20:31",
        "isDelete": false,
        "id": "002ffd73-c3a4-4ada-be39-baa0afdbca45"
      },
      {
        "city": "广州分公司",
        "region": "东区",
        "county": "天河区",
        "gridName": "车陂网格",
        "siteName": "天河区智景花苑",
        "siteCode": "440106500000000203",
        "operationId": "44010600001521",
        "belongs": "",
        "moveSite": "智景花苑",
        "unicomSite": null,
        "telecomSite": "天河区智景花苑",
        "creator": "",
        "creatorId": "",
        "createTime": "2024-08-13 16:20:31",
        "updator": "",
        "updatorId": "",
        "updateTime": "2024-08-13 16:20:31",
        "isDelete": false,
        "id": "0030c8f3-4cc8-49b2-b0f6-fb10253e7625"
      },
      {
        "city": "广州分公司",
        "region": "南区",
        "county": "番禺区",
        "gridName": "石楼网格",
        "siteName": "广州-FG-077-佛莞官桥站六GS-HFR",
        "siteCode": "440113500010004107",
        "operationId": "44011300006606",
        "belongs": "",
        "moveSite": "广州FG番禺区广州-FG-077-番禺区佛莞官桥站六DF-HLR",
        "unicomSite": null,
        "telecomSite": null,
        "creator": "",
        "creatorId": "",
        "createTime": "2024-08-13 16:20:31",
        "updator": "",
        "updatorId": "",
        "updateTime": "2024-08-13 16:20:31",
        "isDelete": false,
        "id": "004ca66b-e099-40d0-add2-67ea6aa00820"
      },
      {
        "city": "广州分公司",
        "region": "南区",
        "county": "番禺区",
        "gridName": "市桥网格",
        "siteName": "广州福怡路三中五替5G新建",
        "siteCode": "440113500000003910",
        "operationId": "44011300006199",
        "belongs": "",
        "moveSite": null,
        "unicomSite": "广州福怡路三中五替5G新建",
        "telecomSite": null,
        "creator": "",
        "creatorId": "",
        "createTime": "2024-08-13 16:20:31",
        "updator": "",
        "updatorId": "",
        "updateTime": "2024-08-13 16:20:31",
        "isDelete": false,
        "id": "00565cce-92f3-4105-b7df-0b83be0c7bcc"
      }
    ],
    "columnHeaders": []
  }
)
const OperatorListMstsExport = () => { }
const OperatorListMstsDownload = () => { }
const getToken = () => { }

export default {
  data() {
    return {
      tableList: [],
      fileList: [],
      listQuery: {
        page: 1,
        limit: 10
      },
      listLoading: false,
      loadingTemplate: false,
      loadingExport: false,
      btnLoading: false,
      total: 0,
      tokenHeader: { "X-Token": getToken() },
      baseURL: import.meta.env.VUE_APP_BASE_API,
    }
  },
  components: {
    pageLayout,
    WechatDetail
  },
  mounted() {
    this.getList()
  },
  methods: {
    async getList() {
      this.listLoading = true;
      let { data, count } = await OperatorListMstsLoad(this.listQuery);
      this.total = count;
      this.tableList = data;
      this.listLoading = false;
    },
    tableRowClassName({ rowIndex }) {
      return rowIndex % 2 == 0 ? "rowStyle" : "rowStyleWhite";
    },
    async handleDetail(row) {
      this.$refs.wechatDetail.initForm(row);
    },
    handleTemplate() {
      const templateName = '运营商站址管理.xlsx'
      const url = `${process.env.VUE_APP_BASE_TEMPLATE_URL}/${templateName}`
      window.open(url, '_blank')
    },
    uploadSuccess(res) {
      let filePath = ''
      if (res.code === 200) {
        this.$message.success(res.message || res.msg || '导入成功');
        this.handleFilter();
      } else {
        this.$message.error(res.message || res.msg || '导入失败');
        filePath = res?.data?.filePath || res?.data || ''
        filePath && this.getOutputFile(filePath);
      }
      this.btnLoading = false;
    },
    getOutputFile(filePath) {
      const url = process.env.VUE_APP_BASE_IMG_URL + '/' + filePath
      window.open(url, '_blank')
    },
    handleDownLoad() {
      try {
        this.loadingTemplate = true;
        OperatorListMstsDownload().then(res => {
          let blob = new Blob([res.data], { type: 'application/vnd.ms-excel' })
          let link = document.createElement('a')
          link.style.display = 'none'
          link.href = URL.createObjectURL(blob)
          link.setAttribute('download', '运营商站址信息导入模板' + '.xlsx')
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          this.loadingTemplate = false;
        })
      } catch (e) {
        this.$message({
          message: '请求未响应',
          type: 'error'
        })
      }
    },
    handleExport() {
      try {
        this.loadingExport = true;
        let data = { ...this.listQuery }
        OperatorListMstsExport(data).then(res => {
          let blob = new Blob([res.data], { type: 'application/vnd.ms-excel' })
          let link = document.createElement('a')
          link.style.display = 'none'
          link.href = URL.createObjectURL(blob)
          link.setAttribute('download', '运营商站址信息管理' + '.xlsx')
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          this.loadingExport = false;
        })
      } catch (e) {
        this.$message({
          message: '请求未响应',
          type: 'error'
        })
      }
    },
    handleCurrentChange(val) {
      this.listQuery.page = val.page;
      this.listQuery.limit = val.limit;
      this.getList();
    },
    handleClick(type) {
      if (type === "submit") {
        this.handleFilter()
      } else if (type === "reset") {
        this.handleReset()
      } else if (type === "refresh") {
        this.handleRefresh()
      }
    },
    refreshBox() {
      this.handleFilter();
    },
    handleFilter() {
      this.listQuery.page = 1;
      this.getList();
    },
    handleReset() {
      this.listQuery = {
        page: 1,
        limit: 10
      }
      this.getList();
    },
    handleRefresh() {
      this.getList();
    },
  }
}
</script>
<style scoped>
:v-deep(.sticky) {
  margin: 10px 0 0;
  background-color: #fff;
  border-bottom: 1px solid #CD202D;
}
</style>
