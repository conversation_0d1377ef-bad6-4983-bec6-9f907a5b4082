<template>
  <el-dialog :title="this.isEdit ? '运营商站址编辑' : '运营商站址详情'" v-model="dialogVisible" :close-on-click-modal="false"
    destroy-on-close @close="handleClose" width="80%" class="common-dialog common-dialog-form common-dialog-body-10">
    <el-form label-width="120px" ref="form" :model="form">
      <el-row>
        <el-col :span="6">
          <el-form-item :label="'地市：'" prop="city">
            <div>{{ form.city }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="'区县：'" prop="county">
            <div>{{ form.county }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="'网格名称：'" prop="gridName">
            <div>{{ form.gridName }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="'站址名称：'" prop="siteName">
            <div>{{ form.siteName }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="'站址编码：'" prop="siteCode">
            <div>{{ form.siteCode }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="'运维ID：'" prop="operationId">
            <div>{{ form.operationId }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="'所属运营商：'" prop="belongs">
            <div>{{ form.belongs }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="'移动站址名称：'" prop="moveSite">
            <el-input v-if="isEdit" v-model="form.moveSite" placeholder="请输入移动站址名称" class="input-width">
            </el-input>
            <div v-else>{{ form.moveSite }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="'联通站址名称：'" prop="unicomSite">
            <el-input v-if="isEdit" v-model="form.unicomSite" placeholder="请输入联通站址名称" class="input-width">
            </el-input>
            <div v-else>{{ form.unicomSite }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="'电信站址名称：'" prop="telecomSite">
            <el-input v-if="isEdit" v-model="form.telecomSite" placeholder="请输入电信站址名称" class="input-width">
            </el-input>
            <div v-else>{{ form.telecomSite }}</div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer class="dialog-footer">
      <el-button v-if="!isEdit" type="warning" @click="handleEdit">编 辑</el-button>
      <el-button v-else type="success" @click="handleSave">保 存 </el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </el-dialog>
</template>
<script>
// import { OperatorListMstsAdd, OperatorListMstsUpdate } from "@/api/operator";

const OperatorListMstsAdd = () => ({
  code: 200,
  msg: '保存成功',
  data: {}
})

const OperatorListMstsUpdate = () => ({
  code: 200,
  msg: '保存成功',
  data: {}
})

export default {
  name: 'WechatDetail',
  data() {
    return {
      title: '运营商站址详情',
      dialogVisible: true,
      form: {
        id: ''
      },
      isEdit: false
    };
  },
  methods: {
    initForm(data) {
      this.dialogVisible = true;
      if (data) {
        this.form = {
          ...this.form,
          ...data
        }
      }
    },
    handleSave() {
      this.$refs.form.validate(async (valid) => {
        if (!valid) return false;
        let http = this.form.id ? OperatorListMstsUpdate : OperatorListMstsAdd;
        let { code } = await http(this.form);
        if (code == 200) {
          this.$message({
            type: 'success',
            message: '保存成功'
          });
          this.handleClose();
          this.$emit("on-refresh");
        }
      });
    },
    handleClose() {
      this.close();
    },
    close() {
      this.isEdit = false;
      this.dialogVisible = false;
      this.form = {
        id: ''
      }
    },
    handleEdit() {
      this.isEdit = true
    }
  }
}
</script>
<style lang="scss" scoped>
.input-width {
  width: 95%;
}

.collapse-main {
  :deep(.el-collapse-item__arrow) {
    position: absolute;
    font-weight: 700px;
  }

  :deep(.el-icon-arrow-right:before) {
    font-weight: 700 !important;
  }
}

.collapse-title-length-6 :deep(.el-collapse-item__arrow) {
  left: 105px;
}

.collapse-title-length-4 :deep(.el-collapse-item__arrow) {
  left: 108px;
}

.collapse-title-length-4,
.collapse-title-length-6 {
  margin-top: 8px;

  .el-row {
    padding-top: 15px;
  }
}

.common-dialog-body-10 {
  :deep(.el-dialog__body) {
    padding: 10px 10px 0;
  }

  :deep(.el-loading-spinner) {
    top: 20% !important;
  }

  :deep(.el-dialog__footer) {
    margin-top: 2px;
  }
}
</style>