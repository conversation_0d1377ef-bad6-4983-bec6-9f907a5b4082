<template>
  <div
    class="ScaleBox"
    ref="ScaleBox"
    :style="{
      width: width + 'px',
      height: height + 'px',
    }"
  >
    <slot></slot>
  </div>
</template>
<script>
export default {
  name: 'ScaleBox',
  props: {},
  data() {
    return {
      scale: '',
      width: 1920,
      height: 1080,
      debouncedSetScale: null,
    }
  },
  mounted() {
    this.setScale()
    this.debouncedSetScale = this.debounce(this.setScale, 100)
    window.addEventListener('resize', this.debouncedSetScale)
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.debouncedSetScale)
  },
  methods: {
    getScale() {
      let { width, height } = this
      let wh = window.innerHeight / height
      let ww = window.innerWidth / width
      console.log(ww < wh ? ww : wh)
      return ww < wh ? ww : wh
    },
    setScale() {
      // if (window.innerHeight == 1080) {
      //   this.height = 1080
      // } else {
      //   this.height = 937
      // }
      if (window.innerHeight >= 1080) {
        this.scale = this.getScale()
        if (this.$refs.ScaleBox) {
          this.$refs.ScaleBox.style.setProperty(
            'transform',
            `scale(${this.scale}) translate(-50%, -50%)`
          )
          this.$refs.ScaleBox.style.setProperty('left', '50%')
          this.$refs.ScaleBox.style.setProperty('top', '50%')
        }
      } else {
        if (this.$refs.ScaleBox) {
          this.$refs.ScaleBox.style.setProperty('transform', 'none')
          this.$refs.ScaleBox.style.setProperty('left', '0')
          this.$refs.ScaleBox.style.setProperty('top', '0')
        }
      }
    },
    debounce(fn, delay) {
      let delays = delay || 500
      let timer
      return (...args) => {
        if (timer) {
          clearTimeout(timer)
        }
        timer = setTimeout(() => {
          timer = null
          fn.apply(this, args)
        }, delays)
      }
    },
  },
}
</script>

<style lang="scss">
.ScaleBox {
  position: absolute;
  display: flex;
  flex-direction: column;
  transform-origin: 0 0;
  transition: 0.3s;
  z-index: 999;
}
</style>
