<template>
  <div
    class="ScaleBox"
    ref="ScaleBox"
    :style="{
      width: width + 'px',
      height: height + 'px',
    }"
  >
    <slot></slot>
  </div>
</template>
<script>
export default {
  name: 'ScaleBox',
  props: {},
  data() {
    return {
      scale: '',
      width: 1920,
      height: 1080,
    }
  },
  mounted() {
    this.setScale()
    window.addEventListener('resize', this.debounce(this.setScale, 100))
  },
  methods: {
    getScale() {
      let { width, height } = this
      let wh = window.innerHeight / height
      let ww = window.innerWidth / width
      console.log(ww < wh ? ww : wh)
      return ww < wh ? ww : wh
    },
    setScale() {
      // if (window.innerHeight == 1080) {
      //   this.height = 1080
      // } else {
      //   this.height = 937
      // }
      if (window.innerHeight >= 1080) {
        this.scale = this.getScale()
        if (this.$refs.ScaleBox) {
          this.$refs.ScaleBox.style.setProperty('transform-origin', 'center');
          this.$refs.ScaleBox.style.setProperty('transform',`scale(${this.scale})`)
          const marginLeft = -this.$refs.ScaleBox.offsetWidth / 2;
          const marginTop = -this.$refs.ScaleBox.offsetHeight / 2;
          this.$refs.ScaleBox.style.setProperty('left', '50%')
          this.$refs.ScaleBox.style.setProperty('top', '50%')
          this.$refs.ScaleBox.style.setProperty('margin-left', `${marginLeft}px`);
          this.$refs.ScaleBox.style.setProperty('margin-top', `${marginTop}px`);
        }
      } else {
        if (this.$refs.ScaleBox) {
          this.$refs.ScaleBox.style.setProperty('transform-origin', '0 0');
          this.$refs.ScaleBox.style.setProperty('transform', 'scale(1)');
          this.$refs.ScaleBox.style.setProperty('left', '0')
          this.$refs.ScaleBox.style.setProperty('top', '0')
          this.$refs.ScaleBox.style.setProperty('margin-left', `0`);
          this.$refs.ScaleBox.style.setProperty('margin-top', `0`);
        }
      }
    },
    debounce(fn, delay) {
      let delays = delay || 500
      let timer
      return function () {
        let th = this
        let args = arguments
        if (timer) {
          clearTimeout(timer)
        }
        timer = setTimeout(function () {
          timer = null
          fn.apply(th, args)
        }, delays)
      }
    },
  },
}
</script>

<style lang="scss">
.ScaleBox {
  position: absolute;
  display: flex;
  flex-direction: column;
  transform-origin: center;
  transition: 0.3s;
  z-index: 999;
}
</style>
