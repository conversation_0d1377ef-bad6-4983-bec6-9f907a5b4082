<template>
  <div class="page-layout app-container flex-column">
    <slot name="page-tbas"></slot>
    <el-collapse v-model="activeNames">
      <el-collapse-item name="1" class="page-query">
        <template #title>
          <span class="page-sidebar page-query-typeface">查询条件</span>
        </template>
        <slot name="layout-query"></slot>
        <div class="page-query-bottom">
          <el-button type="primary"  @click="handleFilter">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </div>
      </el-collapse-item>
    </el-collapse>

    <sticky class="sticky-container">
      <template #default>
        <div class="page-sticky-template">
          <div class="page-sidebar page-query-typeface">清单列表</div>
          <div class="page-sticky-btn">
            <slot name="sticky-bar"></slot>
            <el-button @click="handleRefresh">刷新</el-button>
          </div>
        </div>
      </template>
    </sticky>

    <div class="flex-item">
      <div class="page-table-top"></div>
      <slot name="page-result"></slot>
    </div>
  </div>
</template>
<script>
import Sticky from "@/components/base/Sticky";
export default {
  name: 'page-layout',
  components: {
    Sticky
  },
  data() {
    return {
      activeNames: ['1'],
    }
  },
  methods: {
    handleFilter() {
      this.$emit('on-submit')
    },
    handleReset() {
      this.$emit('on-reset')
    },
    handleRefresh() {
      this.$emit('on-refresh')
    }
  }
}
</script>
<style lang="scss" scoped></style>