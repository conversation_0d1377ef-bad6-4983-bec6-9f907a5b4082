<template>
  <el-row>
    <el-col :span="6" v-if="cityOptions">
      <div class="page-grid-content">
        <span class="page-query-title">地市分公司：</span>
        <el-select
          v-model="listQuery.city"
          @change="cityChange"
          clearable
          placeholder="请选择内容"
          class="page-box-width"
          size="small">
          <el-option
            v-for="item in cityData"
            :key="item.value"
            :value="item.city"
            :label="item.city">
          </el-option>
        </el-select>
      </div>
    </el-col>
    <el-col :span="6" v-if="countyOptions">
      <div class="page-grid-content">
        <span class="page-query-title">区县：</span>
        <el-select
          v-model="listQuery.county"
          @change="countyChange"
          clearable
          placeholder="请选择内容"
          class="page-box-width"
          size="small">
          <el-option
            v-for="item in countyData"
            :key="item.value"
            :value="item.county"
            :label="item.county">
          </el-option>
        </el-select>
      </div>
    </el-col>
    <el-col :span="6" v-if="gridNameOptions">
      <div class="page-grid-content">
        <span class="page-query-title">网格：</span>
        <el-select
          v-model="listQuery.gridName"
          clearable
          placeholder="请选择内容"
          class="page-box-width"
          size="small"
          @change="gridNameChange">
          <el-option
            v-for="item in gridNameData"
            :key="item.value"
            :value="item.gridName"
            :label="item.gridName">
          </el-option>
        </el-select>
      </div>
    </el-col>
    <el-col :span="6" v-if="!gridNameOptions">
      <slot name="query-placeholder1"></slot>
    </el-col>
    <el-col :span="6">
      <slot name="query-placeholder2"></slot>
    </el-col>
  </el-row>
</template>

<script>
// import { LoadGroupCityData, LoadGroupCountyData, ChttBaseGridMapperMstsLoad } from "@/api/forewarn";
export default {
  name: 'city-county-grid',
  props: {
    LoadGroupCityData: {
      type: Function,
      default: () => {
        return LoadGroupCityData || (() => {})
      }
    },
    LoadGroupCountyData: {
      type: Function,
      default: () => {
        return LoadGroupCountyData || (() => {})
      }
    },
    ChttBaseGridMapperMstsLoad: {
      type: Function,
      default: () => {
        return ChttBaseGridMapperMstsLoad || (() => {})
      }
    },
    filed: {
      type: Object,
      default: () => {
        return {
          city: 'City',
          county: 'County',
          gridName: 'GridName'
        }
      }
    },
    value: {
      type: Object,
      default: () => {
        return {
          page: 1,
          limit: 10,
          city: "",
          county: "",
          gridName: "",
          options: [],
          isAll: false
        }
      }
    }
  },
  watch: {
    value: {
      handler(val) {
        this.listQuery = val
        if(val.city === "") {
          this.countyData = [];
          this.gridNameData = [];
        }
      },
      deep: true
    }
  },
  data() {
    return {
      cityData: [],
      countyData: [],
      gridNameData: [],
      listQuery: {
        page: 1,
        limit: 10,
        city: "",
        county: "",
        gridName: "",
        options: []
      },
      cityOptions: false,
      countyOptions: false,
      gridNameOptions: false,
    }
  },
  mounted() {
    this.listQuery = this.value;
    this.getDataByParams();
    if(this.listQuery.options) {
      this.listQuery.options.forEach(option => {
        if (option === '地市') {
          this.cityOptions = true;
        } else if (option === '区县') {
          this.countyOptions = true;
        } else if (option === '网格') {
          this.gridNameOptions = true;
        }
      });
    }
  },
  methods: {
    async getDataByParams() {
      let { data } = await LoadGroupCityData()
      this.cityData = data
    },
    async getDataByParamsCounty() {
      let { data } = await LoadGroupCountyData({ [this.filed.city]: this.listQuery.city })
      if(this.listQuery.isAll) {
        this.countyData = data.filter((item) => {
          return item.county !== '全部区域';
        });
      } else {
        this.countyData = data
      }
    },
    async getDataByParamsGrid() {
      let { data } = await ChttBaseGridMapperMstsLoad({ [this.filed.city]: this.listQuery.city, [this.filed.county]: this.listQuery.county })
      this.gridNameData = data
    },
    cityChange(val) {
      this.listQuery.county = "";
      this.listQuery.gridName = "";
      this.countyData = [];
      this.gridNameData = [];
      if(val) {
        this.getDataByParamsCounty();
      }
      this.$emit('on-change',this.listQuery)
    },
    countyChange(val){
      this.listQuery.gridName = "";
      this.gridNameData = [];
      if(val) {
        this.getDataByParamsGrid();
      }
      this.$emit('on-change',this.listQuery)
    },
    gridNameChange(){
      this.$emit('on-change',this.listQuery)
    },
  },

}
</script>
