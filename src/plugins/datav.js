// 从库中导入所有需要的组件和方法
import {
  BorderBox1,
  BorderBox2,
  BorderBox3,
  BorderBox4,
  BorderBox5,
  BorderBox6,
  BorderBox7,
  BorderBox8,
  BorderBox9,
  BorderBox10,
  BorderBox11,
  BorderBox12,
  BorderBox13,
  Decoration1,
  Decoration2,
  Decoration3,
  Decoration4,
  Decoration5,
  Decoration6,
  Decoration7,
  Decoration8,
  // Decoration9,
  // Decoration10,
  // Decoration11,
  // Decoration12,
  Loading,
  // setClassNamePrefix
} from '@dataview/datav-vue3/es/index.mjs';

// 创建一个包含所有组件的数组
const components = [
  BorderBox1,
  BorderBox2,
  BorderBox3,
  BorderBox4,
  BorderBox5,
  BorderBox6,
  BorderBox7,
  BorderBox8,
  BorderBox9,
  BorderBox10,
  BorderBox11,
  BorderBox12,
  BorderBox13,
  Decoration1,
  Decoration2,
  Decoration3,
  Decoration4,
  Decoration5,
  Decoration6,
  Decoration7,
  Decoration8,
  // Decoration9,
  // Decoration10,
  // Decoration11,
  // Decoration12,
  Loading,
  // setClassNamePrefix
];

// 创建一个Vue插件对象
const DataV = {
  install(app, { classNamePrefix } = {}) {
    // 遍历并注册所有组件为全局组件
    components.forEach(component => {
      app.component(component.name, component);
      app.component('dv-' + component.name, component);
      app.component('dv' + component.name, component);
    });

    // 如果提供了前缀，则设置
    if (classNamePrefix) {
      setClassNamePrefix(classNamePrefix);
    }
  }
};

// 将这个插件作为默认导出
export default DataV;