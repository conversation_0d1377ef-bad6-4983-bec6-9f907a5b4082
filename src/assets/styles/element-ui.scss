// cover some element-ui styles
.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-menu--collapse
  > div
  > .el-submenu
  > .el-submenu__title
  .el-submenu__icon-arrow {
  display: none;
}

.el-dropdown .el-dropdown-link{
  color: var(--el-color-primary) !important;
}

// custom element-ui styles

.el-menu .el-menu-item i {
  color: #303133;
}

.el-menu .el-submenu__title i {
  color: #303133;
}

.el-collapse-item__arrow {
  margin: 0 20px 0 auto;
}

.el-collapse-item__content {
  padding: 0 0px 0 0;
}

.el-collapse-item__header.is-active {
  border-bottom: 1px solid #CD202D !important;
  height: 42px !important;
}

.el-collapse-item__header {
  height: 42px !important;
}

.el-collapse .el-collapse-item__content {
  padding-bottom: 4px;
}

.el-tabs__item {
  padding: 0 20px !important;
}

.el-tabs__item {
  color: #000;
  opacity: 1;
  border: 1px solid #DCDCDC;
  background-color: #FFF;
  margin-left: -1px;
}
.el-tabs__content {
  overflow: visible;
}

.el-table__header thead th {
  // text-align: left !important;
  height: 50px;
}

.el-table tr {
  height: 50px;
}

.el-dialog {
  font-size: 14px;
  padding: 0;
  .el-dialog__title {
    font-weight: 600;
  }
  .el-dialog__footer {
    padding: 10px 20px;
    text-align: center;
    border-top: 1px solid #CD202D;
  }
  .el-dialog__header {
    border-bottom: 1px solid #CD202D;
    padding: 15px 0px;
  }
  .el-dialog__body {
    padding: 20px 10px 0 10px;
  }
  .el-dialog__headerbtn .el-dialog__close {
    color: #303133;
  }
}


.el-table__row.rowStyle {
  background: #f0f2f5;
}

.el-table__row.rowStyleWhite {
  background: #FFF;
}


.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover{
  color:#C4414B;
  background-color: #FCEBEC;
}

.el-select .el-input.is-disabled .el-input__inner{
  color: #A8ABB2;
}

.el-button {
  height: 32px;
}

button.el-button {
  font-size: 12px;
}

.el-form-item__label,
.el-form-item__content{
  font-size: 14px;
}
