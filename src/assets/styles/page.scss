.page-sidebar::before {
  content: '';
  border-left: 3px solid #D8434E;
  padding: 3px 0 3px 10px;
}

.page-query-typeface {
  font-size: 17px;
  color: #303133;
  font-weight: 600;
}

.page-sticky-template {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-sticky-btn {
  height: 42px;
  line-height: 40px;
}

.page-query {
  width: 100%;
  background-color: #fff;
  margin-bottom: 5px;
}

.page-query-title {
  display: inline-block;
  width: 124px;
  margin-left: 20px;
  padding-right: 10px;
  text-align: right;
  font-size: 14px;
}

.page-query-bottom {
  text-align: center;
  margin: 10px 0 10px 0;
}

.page-grid-content {
  display: flex;
  align-items: center;
  margin: 10px 0;
}

.page-box-width {
  flex: 1;
}

.page-pagination {
  text-align: right;
}

.page-batch {
  margin: 0 10px;
}

.page-input-btn {
  display: inline;
}

.page-table-header {
  text-align: left !important;
  background: #f7f8fa;
  height: '45px';
}

.page-table-style {
  width: 100%;
}
.page-table-top {
  height: 8px;
  background-color: #fff;
}

.page-red-btn {
  border: 1px solid #D8434E;
  color: #D8434E;
  background-color: transparent;
}

.page-green-btn {
  border: 1px solid #40A443;
  background: transparent;
  color: #40A443;
}

.page-blue-btn {
  border: 1px solid #00A8AF;
  background: transparent;
  color: #00A8AF;
}

.page-footer-toolbar {
  width: calc(100% - 200px);
  height: 55px;
  background: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  bottom: 0;
  right: 0;
  box-shadow: 0 -1px 2px #00000008;
  border-top: 1px solid red;
  z-index: 999;
}

.page-footer-toolbar-false {
  width: calc(100% - 36px);
  height: 55px;
  background: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  bottom: 0;
  right: 0;
  box-shadow: 0 -1px 2px #00000008;
  border-top: 1px solid red;
  z-index: 999;
}

.page-placeholder {
  height: 10px;
}
.page-input-width {
  width: 95%;
}