// .[modules]-[component]-[desc] {
// }

/* <el-tag ... hit class="common-tag-gray hit"></el-tag> */
.common-tag-gray.el-tag.is-hit {
  color: #65676B;
  background-color: #EFF0F3;
  &.hit {
    border-color: #C8CBCC;
  }
}

.common-dialog-subtitle {
  display: flex;
  align-items: center;
  text-indent: 16px;
  height: 36px;
  font-size: 18px;
  font-weight: 500;
  margin: 10px 0;
  &.red {
    background-color: #FCEBEC;
  }
  &.green {
    background-color: #ebf6ea;
  }
}
.common-dialog-body-10 {
  .el-dialog__body {
    padding: 10px 10px 0;
  }
}

.common-dialog-body-20 {
  .el-dialog__body {
    padding: 20px 20px 0 0;
  }
}

.common-dialog-form {
  .el-dialog__header::before {
    content: '';
    border-left: 3px solid #D8434E;
    padding: 3px 0 3px 10px;
    height: 18px;
  }
}

.common-dialog-formContent {
  .el-input.is-disabled .el-input__inner {
    background-color: #fff;
    color: #303133;
  }
  .el-radio__input.is-disabled .el-radio__inner {
    background-color: #fff;
  }
  .el-radio__input.is-disabled.is-checked .el-radio__inner::after {
    background-color: #D8434E;
    width: 6px;
    height: 6px;
  }
  .el-radio__input.is-disabled+span.el-radio__label {
    color: #303133;
  }
  .el-input.is-disabled .el-input__inner {
    cursor: auto;
  }
}

.common-tag-green.el-tag.is-hit {
  color: #40A443;
  background-color: #eef7ec;
  &.hit {
    border-color: #40A443;
  }
}
.common-tag-yellow.el-tag.is-hit {
  color: #D69251;
  background-color: #FBF3EB;
  &.hit {
    border-color: #D69251;
  }
}

.common-tabs {
  .el-tabs__header {
    margin: 0;
  }
  .el-tabs__nav-scroll {
    height: 42px;
  }
  &.el-tabs .el-tabs__nav-wrap::after {
    background-color: transparent;
  }
  &.el-tabs .el-tabs__item {
    border: none;
  }
}

.common-label .cell {
  font-weight: 700;
  font-size: 20px;
}

.common-dialog {
  [class*=el-col-] {
    min-height: 40px;
  }
}

.common-table-entirety {
  thead th.el-table__cell.header-cell {
    background: #fff !important;
    color: #65676B;
    font-weight: 400;
  }
  .el-table__header thead th, .page-table-header, .el-table__cell {
    text-align: center !important;
  }
  .cell-pink-bg {
    background-color: #F4CDCD;
  }
  .bold-label .cell {
    font-weight: 700;
    font-size: 20px;
  }
}

.common-card-warnConfig {
  .el-card__body {
    padding:  0 0 10px 0;
  }
  .el-tabs__nav-scroll {
    background-color: #fff;
  }
  .el-tabs__item {
    border: none;
  }
  .el-tabs__nav-scroll {
    background-color: #fff;
  }
  .el-tabs__nav-wrap::after {
    background-color: transparent;
  }
  .el-tabs__header {
    margin: 0;
  }
}

.common-dialog-sceneDetail {
  .pagination-container {
    background: linear-gradient(rgba(5, 20, 46, 0.75),rgba(7, 35, 61, 0.75)) !important;
    border: 0;
  }
  .el-form .el-form-item {
    margin: 6px;
  }
  .el-select .el-input.is-focus .el-input__inner {
    border: 1px solid #31c8f3;
  }
  .el-pagination .el-select .el-input .el-input__inner:hover {
    border: 1px solid #31c8f3;
  }
  .el-table .el-table__cell.is-center {
    text-align: left;
  }
  .el-table th.el-table__cell>.cell {
    text-align: left !important;
  }
  .el-form-item--medium .el-form-item__label {
    font-size: 18px;
  }
}

.common-dialog-sceneIndex {
  .el-form-item__label {
    color: #fff;
    font-size: 17px;
    font-weight: normal;
  }

  .el-form-item__content {
    color: #fff;
    font-size: 17px;
    width: 160px;
  }

  .el-form-item {
    margin-bottom: 0;
  }
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
  }
}

.common-table-sceneReinsurance {
  .el-table tr {
    background: #070f1a;
    color: #fff;
  }
  .el-table {
    background-color: transparent;
  }
  .el-table--border {
    border: none;
  }
  .el-table th.el-table__cell {
    background: #070f1a;
  }
  .el-table th.el-table__cell > .cell {
    color: #fff;
  }
  .el-table td.el-table__cell,
  .el-table th.el-table__cell.is-leaf {
    border-bottom: 1px solid #1f3463;
    font-size: 16px;
    height: 38px;
  }
  .el-table th.el-table__cell.is-leaf {
    border-bottom: 1px solid #1f3463;
    font-size: 16px;
    height: 56px;
  }
  .el-table--border .el-table__cell {
    border-right: 1px solid #1f3463;
  }
  .el-table--border::after {
    width: 0;
  }
  .el-table::before {
    width: 0;
  }
  .el-table tbody tr:hover > td {
    background-color: transparent !important;
  }
  .el-table__body-wrapper {
    &::-webkit-scrollbar {
      width: 17px;
      height: 12px;
    }
    &::-webkit-scrollbar-thumb {
      // border-radius: 6px;
      background: #1f3463;
      border-radius: 0;
      -webkit-box-shadow: inset 0 0 5px #00000099;
    }
    &::-webkit-scrollbar-track {
      background: transparent;
      -webkit-box-shadow: inset 0 0 5px #0003;
      border-radius: 0;
    }
  }
}
.common-button-size {
  font-size: 14px;
}

.common-imp-details {
  .el-dialog__header {
    border-bottom: 1px solid #1f3463;
    padding:  6px 0 6px 20px;
    background: #0a1f39;
  }
  .el-dialog__title {
    font-family: YouSheBiaoTiHei;
    font-size: 30px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    background: linear-gradient(180deg, #50B8FF 0%, #F7F7F7 57.81%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .el-dialog__headerbtn .el-dialog__close {
    color: #fff;
  }
  .el-dialog__body {
    padding: 0;
  }
  .el-table {
    background-color: transparent;
    &::before {
      background-color: transparent;
    }

    thead {
      color: #BACAE5;
      font-size: 18px;
      background-image: url('~@/components/big-screen/imgs/img_list_header.png');
      background-size: 100% 100%;
      th {
        height: 50px;
      }
    }

    .el-table__body-wrapper {
      background-size: 100% 100%;
    }

    td.el-table__cell,
    th.el-table__cell.is-leaf {
      border-bottom: 0;
    }

    th.el-table__cell {
      background-color: transparent;
    }

    tr {
      color: #E6EAF5;
      font-size: 18px;
      height: 42px;
      background-color: transparent;
      border-bottom: 2px solid rgba(87, 146, 201, 0.60);
    }
    th.el-table__cell>.cell {
      text-align: center;
    }
  }
  .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
    background-color: transparent;
    background-color: rgba(46, 87, 125, 0.40);
    border-bottom: 2px solid rgba(87, 146, 201, 0.60);
  }

  .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell {
    background-color: #2e577d;
  }
  .left-bottom-table {
    .el-table__cell>.cell {
      text-align: center;
      font-size: 15px;
      padding-left: 4px;
      padding-right: 4px;
    }
    .el-table td.el-table__cell div {
      padding-left: 0;
      padding-right: 0;
      text-overflow: ellipsis;
      font-size: 14px;
    }
  }
  .el-form-item--medium .el-form-item__label {
    color: #FFF;
    text-align: right;
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
  }
  .el-pagination {
    .el-pagination__total {
      color: rgba(255, 255, 255, 1);
    }
    .el-pagination__jump {
      color: rgba(255, 255, 255, 1);
    }
    &.is-background .el-pager li {
      background-color: rgba(255, 255, 255, 0.7);
      color: rgba(0, 0, 0, 0.9);
    }
    &.is-background .el-pager li:not(.disabled).active {
      background-color: rgba(26, 105, 128, 1);
      border: 1px solid rgba(42, 172, 209, 1);
      color: rgba(51, 211, 255, 0.9);
    }
    &.is-background .el-pager li:hover {
      background-color: rgba(26, 105, 128, 1);
      color: rgba(51, 211, 255, 0.9);
    }
    &.is-background .btn-next:disabled, &.is-background .btn-prev:disabled, &.is-background .btn-prev, &.is-background .btn-next {
      background-color: transparent !important;
    }
  }
  .el-input {
    .el-input__inner {
      background-color: rgba(255, 255, 255, 0.1);
      color: #fff;
      border: 1px solid rgba(87, 146, 201, 1);
    }
  }
  .el-table__body-wrapper::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    background-color: rgba(204, 204, 204, 0.3);
  }
  .el-table__body-wrapper::-webkit-scrollbar-thumb {
    background-color: rgba(149, 181, 229, 0.9);
    border-radius: 6px;
  }
}