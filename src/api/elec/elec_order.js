import request from '@/utils/request'

// 地市、区县级查询接口
export function getOrderList(query) {
  return request({
    url: '/elec/elec_order/statisticalList',
    method: 'get',
    params: query
  })
}

// 发电工单分页查询
export function getElecOrderPage(params) {
  return request({
    url: '/elec/elec_order/page',
    method: 'get',
    params
  })
}

// 发电工单查看详情
export function getElecOrderDetail(query) {
  return request({
    url: '/elec/elec_order/get',
    method: 'get',
    params: query
  })
}

// 获取曲线图
export function getCurvedSurvey(faultCode) {
  return request({
    url: '/elec/elec_order/curvedSurvey',
    method: 'get',
    params: { faultCode }
  })
}

// 下载附件
export function downloadAttachment(attachId) {
  return request({
    url: '/elec/elec_order/downAttach',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    responseType: 'blob',
    method: 'get',
    params: { id: attachId }
  })
}

// 获取地市列表
export function getListCity(regionCode = '530000') {
  return request({
    url: '/elec/area/listCity',
    method: 'get',
    params: { regionCode }
  })
}

// 获取区县列表
export function getListCounty(regionCode) {
  return request({
    url: '/elec/area/listCounty',
    method: 'post',
    data: regionCode
  })
}

// 地市、区县报表导出接口
export function exp(params) {
  return request({
    url: '/elec/elec_order/exportExcel',
    method: 'get',
    params,
    responseType: 'blob' // 保留文件下载特性
  })
}

// 清单级报表导出接口
export function Listexp(params) {
  return request({
    url: '/elec/elec_order/exportOrderExcel',
    method: 'get',
    params,
    responseType: 'blob' // 保留文件下载特性
  })
}
