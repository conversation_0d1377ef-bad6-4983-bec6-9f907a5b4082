import request from '@/utils/request'

// 查询登录日志列表
export function list(query) {
  return request({
    // url: '/monitor/logininfor/list',
    url: '/system/sys_login_infor/page',
    method: 'get',
    params: query
  })
}

// 删除登录日志
export function delLogininfor(infoId) {
  return request({
    url: '/system/sys_login_infor/delete',
    method: 'delete',
    data: infoId
  })
}

// 解锁用户登录状态
export function unlockLogininfor(userName) {
  return request({
    url: '/system/sys_login_infor/unlock/' + userName,
    method: 'get'
  })
}

// 清空登录日志
export function cleanLogininfor() {
  return request({
    url: '/system/sys_login_infor/clean',
    method: 'delete'
  })
}


// 清单级报表导出接口
export function Listexp(params) {
  return request({
    url: '/system/sys_login_infor/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
