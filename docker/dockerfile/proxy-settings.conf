#root   html;
#index  index.html index.htm;
#转发的时候服务地址的配置 加上了端口
proxy_set_header Host $host:$server_port;
proxy_set_header X-Real-IP $remote_addr;
proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
proxy_set_header X-Forwarded-Proto $scheme;
proxy_set_header Upgrade $http_upgrade;
#proxy_set_header Connection "upgrade";
#清除缓存
proxy_request_buffering off;
#预防超时
uwsgi_read_timeout 3000;
proxy_connect_timeout 3000;
proxy_send_timeout 3000;
proxy_read_timeout 3000;
send_timeout 3000;
fastcgi_connect_timeout 3000;
fastcgi_send_timeout 3000;
fastcgi_read_timeout 3000;
#keepalive_timeout  0;
keepalive_timeout 999999;
