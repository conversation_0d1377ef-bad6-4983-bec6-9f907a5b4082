version: '3'
services:
  $PROJECT_ID:
    image: $PROJECT_ID
    container_name: $PROJECT_ID
    restart: always
    ports:
      - "$DEPLOY_PORT:8080"
    volumes:
      - ./dockerfile/nginx.conf:/etc/nginx/conf.d/default.conf
      - ./dockerfile/proxy-settings.conf:/etc/nginx/proxy-settings.conf
      - ./dockerfile/dist/:/usr/share/nginx/web_html/
      - ./dockerfile/cert/:/etc/nginx/cert/
    networks:
      - openauth

networks:
  openauth:
    external: true
