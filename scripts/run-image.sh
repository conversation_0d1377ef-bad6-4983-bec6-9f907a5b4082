set -ex

# 选择环境配置
PROJECT_ENV=${1:-test}

OS=$(uname -s)

# TODO 判断远端是否存在比当前镜像相等高版本的镜像，若存在则不构建

# 当前bash绝对路径
CUR_PATH=$(dirname $(readlink -f $0));
# docker目录
DOCKER_DIR=$CUR_PATH/../docker;
# tmp目录
TMP=$CUR_PATH/../tmp;

# 使用指定的环境配置
source $CUR_PATH/env-$PROJECT_ENV.sh;

# 清空临时目录
rm -rf $TMP;

# 复制模板目录到临时目录
cp -r $DOCKER_DIR $TMP;
# 复制环境变量配置到临时目录
cp -r $CUR_PATH/env-$PROJECT_ENV.sh $TMP;

# 构建前端代码
pnpm run build:${PROJECT_ENV};
rm -rf $TMP/dockerfile/dist;
mv dist/ $TMP/dockerfile/;

cd $TMP;

# 临时目录中生成指定环境的docker配置
if [ "$OS" == "Darwin" ]; then
  # macOS
  sed -i -E "s/\$API_PATH/$API_PATH/g" docker-compose.yml
  sed -i -E "s/\$PROJECT_ID/$PROJECT_ID/g" docker-compose.yml
  sed -i -E "s/\$DEPLOY_PORT/$DEPLOY_PORT/g" docker-compose.yml
  sed -i -E "s/\$API_PATH/$API_PATH/g" dockerfile/nginx.conf
else
  # Linux
  sed -i "s/\$API_PATH/$API_PATH/g" docker-compose.yml
  sed -i "s/\$PROJECT_ID/$PROJECT_ID/g" docker-compose.yml
  sed -i "s/\$DEPLOY_PORT/$DEPLOY_PORT/g" docker-compose.yml
  sed -i "s/\$API_PATH/$API_PATH/g" dockerfile/nginx.conf
fi

cat docker-compose.yml;
cat dockerfile/nginx.conf;

rm -rf $TMP/docker-compose.yml-E;
rm -rf $TMP/dockerfile/nginx.conf-E;

ssh ${SERVER_USER}@${SERVER_IP} -p ${SERVER_SSH_PORT} "mkdir -p ${DEPLOY_PATH}"

zip -rq ${PROJECT_ID}_docker-${PROJECT_ENV}.zip .

scp -r -P ${SERVER_SSH_PORT} ${PROJECT_ID}_docker-${PROJECT_ENV}.zip ${SERVER_USER}@${SERVER_IP}:${DEPLOY_PATH}

ssh ${SERVER_USER}@${SERVER_IP} -p ${SERVER_SSH_PORT} \
"
  cd    ${DEPLOY_PATH}
  unzip -oq ./${PROJECT_ID}_docker-${PROJECT_ENV}.zip -d .
  bash  ./start_build.sh ${PROJECT_ID} Dockerfile
  docker ps | grep ${PROJECT_ID}
  ls -last /data/web/${PROJECT_ID}
"
