set -ex

# 选择环境配置
PROJECT_ENV=${1:-test}

# 当前bash绝对路径
CUR_PATH=$(dirname $(readlink -f $0));
# docker目录
DIST_DIR=$CUR_PATH/../dist;
# tmp目录
TMP=$CUR_PATH/../tmp;

# 使用指定的环境配置
source $CUR_PATH/env-$PROJECT_ENV.sh;

# 清空临时目录
rm -rf $TMP;

# 构建前端代码
pnpm run build:${PROJECT_ENV};

# 复制构建代码到临时目录
cp -r $DIST_DIR $TMP;
# 复制环境变量配置到临时目录
cp -r $CUR_PATH/env-$PROJECT_ENV.sh $TMP;
# 清空dist目录
rm -rf $DIST_DIR;

cd $TMP;

zip -rq ${PROJECT_ID}_dist-${PROJECT_ENV}.zip .

scp -r -P ${SERVER_SSH_PORT} ${PROJECT_ID}_dist-${PROJECT_ENV}.zip ${SERVER_USER}@${SERVER_IP}:${DEPLOY_PATH}

ssh ${SERVER_USER}@${SERVER_IP} -p ${SERVER_SSH_PORT} \
"
  cd    /data/web/${PROJECT_ID}
  unzip -oq ./${PROJECT_ID}_dist-${PROJECT_ENV}.zip -d ./dockerfile/dist/
  docker ps | grep ${PROJECT_ID}
  ls -last /data/web/${PROJECT_ID}
"
