# web-dockerfile

公网服务，映射内网服务

```shell
# 构建
npm run build:prod
# 清除dist目录
rm -rf web-dockerfile/dist
# 创建dist目录
mkdir -p web-dockerfile/dist
# 复制 dist 到 web-dockerfile/dist
cp -r dist/* web-dockerfile/dist

cd web-dockerfile

# 镜像
docker build -f Dockerfile -t rjgf-yn-elec-order-web:1.0.0 .
# 转换（.tag 200M左右）
docker save rjgf-yn-elec-order-web:1.0.0 -o rjgf-yn-elec-order-web.tar
# 压缩成tar.gz格式
tar zcvf rjgf-yn-elec-order-web.tar.gz rjgf-yn-elec-order-web.tar
```

后续操作：手动上传到http://192.168.10.101:6005/的`yn-eoi-file`目录。


```
上传服务器后：
解压：tar zxvf rjgf-gddc-app.tar.gz
加载镜像：docker load -i rjgf-gddc-app.tar
查看镜像： docker images

启动前端：docker-compose -f docker-compose-app.yml up -d rjgf-gddc-app
```
