# 局域网使用
server {

  listen 8080;
  # listen 8080 ssl;
  server_name localhost;

  # ssl_certificate /etc/nginx/cert/intelli.luoliny.com.pem;
  # ssl_certificate_key /etc/nginx/cert/intelli.luoliny.com.key;

  root /usr/share/nginx/web_html;
  charset utf-8;
  autoindex on;
  client_max_body_size 500m;
  location / {
    autoindex off;
    index index.html index.htm;
    #单页应用  所有url请求都指向index.html
    try_files $uri $uri/ /index.html;
  }

  location /api/ {
    #root   html;
    #index  index.html index.htm;
    proxy_pass ${API_PATH}/;
    #转发的时候服务地址的配置 加上了端口
    proxy_set_header Host $host:$server_port;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header Upgrade $http_upgrade;
    #proxy_set_header Connection "upgrade";
    #清除缓存
    proxy_request_buffering off;
    #预防超时
    uwsgi_read_timeout 3000;
    proxy_connect_timeout 3000s;
    proxy_send_timeout 3000s;
    proxy_read_timeout 3000s;
    send_timeout 3000s;
    fastcgi_connect_timeout 3000;
    fastcgi_send_timeout 3000;
    fastcgi_read_timeout 3000;
    #keepalive_timeout  0;
    keepalive_timeout 999999;
  }
}